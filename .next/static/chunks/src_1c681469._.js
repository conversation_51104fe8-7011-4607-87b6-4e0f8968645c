(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/constants/index.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ANIMATION_PRESETS": ()=>ANIMATION_PRESETS,
    "API_ENDPOINTS": ()=>API_ENDPOINTS,
    "BUBBLE_CONFIG": ()=>BUBBLE_CONFIG,
    "CHART_DIMENSIONS": ()=>CHART_DIMENSIONS,
    "COLORS": ()=>COLORS,
    "DEFAULT_SETTINGS": ()=>DEFAULT_SETTINGS,
    "EXPORT_FORMATS": ()=>EXPORT_FORMATS,
    "LOCAL_STORAGE_KEYS": ()=>LOCAL_STORAGE_KEYS,
    "MARKET_CAP_RANGES": ()=>MARKET_CAP_RANGES,
    "PRICE_CHANGE_RANGES": ()=>PRICE_CHANGE_RANGES,
    "SOCIAL_LINKS": ()=>SOCIAL_LINKS,
    "SUPPORTED_CURRENCIES": ()=>SUPPORTED_CURRENCIES,
    "THEMES": ()=>THEMES,
    "TIME_PERIODS": ()=>TIME_PERIODS,
    "VISUALIZATION_MODES": ()=>VISUALIZATION_MODES,
    "VOLUME_RANGES": ()=>VOLUME_RANGES
});
const VISUALIZATION_MODES = [
    {
        id: 'bubble',
        name: 'Bubble Chart',
        description: 'Interactive bubble chart where size represents market cap and color represents price change'
    },
    {
        id: 'treemap',
        name: 'Treemap',
        description: 'Hierarchical visualization showing market cap proportions'
    },
    {
        id: 'scatter',
        name: 'Scatter Plot',
        description: 'Scatter plot showing price vs volume relationships'
    },
    {
        id: 'heatmap',
        name: 'Heatmap',
        description: 'Grid-based heatmap showing price changes across different time periods'
    }
];
const SUPPORTED_CURRENCIES = [
    {
        code: 'usd',
        name: 'US Dollar',
        symbol: '$'
    },
    {
        code: 'eur',
        name: 'Euro',
        symbol: '€'
    },
    {
        code: 'gbp',
        name: 'British Pound',
        symbol: '£'
    },
    {
        code: 'jpy',
        name: 'Japanese Yen',
        symbol: '¥'
    },
    {
        code: 'btc',
        name: 'Bitcoin',
        symbol: '₿'
    },
    {
        code: 'eth',
        name: 'Ethereum',
        symbol: 'Ξ'
    }
];
const TIME_PERIODS = [
    {
        value: 1,
        label: '24H',
        days: 1
    },
    {
        value: 7,
        label: '7D',
        days: 7
    },
    {
        value: 30,
        label: '30D',
        days: 30
    },
    {
        value: 90,
        label: '90D',
        days: 90
    },
    {
        value: 365,
        label: '1Y',
        days: 365
    }
];
const MARKET_CAP_RANGES = [
    {
        label: 'All',
        min: 0,
        max: Infinity
    },
    {
        label: 'Large Cap (>$10B)',
        min: 10e9,
        max: Infinity
    },
    {
        label: 'Mid Cap ($1B-$10B)',
        min: 1e9,
        max: 10e9
    },
    {
        label: 'Small Cap ($100M-$1B)',
        min: 100e6,
        max: 1e9
    },
    {
        label: 'Micro Cap (<$100M)',
        min: 0,
        max: 100e6
    }
];
const VOLUME_RANGES = [
    {
        label: 'All',
        min: 0,
        max: Infinity
    },
    {
        label: 'High Volume (>$1B)',
        min: 1e9,
        max: Infinity
    },
    {
        label: 'Medium Volume ($100M-$1B)',
        min: 100e6,
        max: 1e9
    },
    {
        label: 'Low Volume (<$100M)',
        min: 0,
        max: 100e6
    }
];
const PRICE_CHANGE_RANGES = [
    {
        label: 'All',
        min: -Infinity,
        max: Infinity
    },
    {
        label: 'Strong Gainers (>+10%)',
        min: 10,
        max: Infinity
    },
    {
        label: 'Gainers (+5% to +10%)',
        min: 5,
        max: 10
    },
    {
        label: 'Moderate (+0% to +5%)',
        min: 0,
        max: 5
    },
    {
        label: 'Moderate (-5% to 0%)',
        min: -5,
        max: 0
    },
    {
        label: 'Losers (-10% to -5%)',
        min: -10,
        max: -5
    },
    {
        label: 'Strong Losers (<-10%)',
        min: -Infinity,
        max: -10
    }
];
const BUBBLE_CONFIG = {
    MIN_RADIUS: 8,
    MAX_RADIUS: 80,
    PADDING: 2,
    ANIMATION_DURATION: 750,
    COLLISION_STRENGTH: 0.7,
    CHARGE_STRENGTH: -30
};
const COLORS = {
    POSITIVE: {
        STRONG: '#00C851',
        MEDIUM: '#00FF41',
        LIGHT: '#4CAF50'
    },
    NEGATIVE: {
        STRONG: '#FF1744',
        MEDIUM: '#FF5722',
        LIGHT: '#F44336'
    },
    NEUTRAL: '#9E9E9E',
    BACKGROUND: {
        LIGHT: '#FFFFFF',
        DARK: '#1A1A1A'
    },
    TEXT: {
        PRIMARY_LIGHT: '#000000',
        PRIMARY_DARK: '#FFFFFF',
        SECONDARY_LIGHT: '#666666',
        SECONDARY_DARK: '#CCCCCC'
    }
};
const THEMES = {
    LIGHT: 'light',
    DARK: 'dark'
};
const DEFAULT_SETTINGS = {
    currency: 'usd',
    theme: THEMES.LIGHT,
    refreshInterval: 30000,
    itemsPerPage: 100,
    visualizationMode: 'bubble',
    showLabels: true,
    showTooltips: true,
    enableAnimations: true
};
const API_ENDPOINTS = {
    COINGECKO_BASE: 'https://api.coingecko.com/api/v3',
    RATE_LIMIT_DELAY: 100,
    TIMEOUT: 10000
};
const LOCAL_STORAGE_KEYS = {
    THEME: 'crypto-bubble-theme',
    CURRENCY: 'crypto-bubble-currency',
    SETTINGS: 'crypto-bubble-settings',
    PORTFOLIO: 'crypto-bubble-portfolio',
    FAVORITES: 'crypto-bubble-favorites'
};
const SOCIAL_LINKS = {
    TWITTER: 'https://twitter.com',
    TELEGRAM: 'https://t.me',
    REDDIT: 'https://reddit.com',
    GITHUB: 'https://github.com'
};
const EXPORT_FORMATS = [
    {
        value: 'png',
        label: 'PNG Image'
    },
    {
        value: 'svg',
        label: 'SVG Vector'
    },
    {
        value: 'pdf',
        label: 'PDF Document'
    },
    {
        value: 'csv',
        label: 'CSV Data'
    },
    {
        value: 'json',
        label: 'JSON Data'
    }
];
const CHART_DIMENSIONS = {
    DEFAULT_WIDTH: 800,
    DEFAULT_HEIGHT: 600,
    MIN_WIDTH: 400,
    MIN_HEIGHT: 300,
    ASPECT_RATIO: 4 / 3
};
const ANIMATION_PRESETS = {
    FAST: {
        duration: 300,
        easing: 'ease-out'
    },
    NORMAL: {
        duration: 500,
        easing: 'ease-in-out'
    },
    SLOW: {
        duration: 750,
        easing: 'ease-in-out'
    },
    SPRING: {
        type: 'spring',
        stiffness: 300,
        damping: 30
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/ThemeContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ThemeProvider": ()=>ThemeProvider,
    "useTheme": ()=>useTheme
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/index.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
const ThemeContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function useTheme() {
    _s();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(ThemeContext);
    if (context === undefined) {
        throw new Error('useTheme must be used within a ThemeProvider');
    }
    return context;
}
_s(useTheme, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function ThemeProvider(param) {
    let { children } = param;
    _s1();
    const [theme, setThemeState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["THEMES"].LIGHT);
    const [mounted, setMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Initialize theme from localStorage or system preference
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ThemeProvider.useEffect": ()=>{
            const savedTheme = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LOCAL_STORAGE_KEYS"].THEME);
            if (savedTheme && (savedTheme === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["THEMES"].LIGHT || savedTheme === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["THEMES"].DARK)) {
                setThemeState(savedTheme);
            } else {
                // Check system preference
                const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                setThemeState(systemPrefersDark ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["THEMES"].DARK : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["THEMES"].LIGHT);
            }
            setMounted(true);
        }
    }["ThemeProvider.useEffect"], []);
    // Apply theme to document
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ThemeProvider.useEffect": ()=>{
            if (!mounted) return;
            const root = document.documentElement;
            if (theme === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["THEMES"].DARK) {
                root.classList.add('dark');
            } else {
                root.classList.remove('dark');
            }
            // Save to localStorage
            localStorage.setItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LOCAL_STORAGE_KEYS"].THEME, theme);
        }
    }["ThemeProvider.useEffect"], [
        theme,
        mounted
    ]);
    const toggleTheme = ()=>{
        setThemeState((prevTheme)=>prevTheme === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["THEMES"].LIGHT ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["THEMES"].DARK : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["THEMES"].LIGHT);
    };
    const setTheme = (newTheme)=>{
        if (newTheme === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["THEMES"].LIGHT || newTheme === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["THEMES"].DARK) {
            setThemeState(newTheme);
        }
    };
    const isDarkMode = theme === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["THEMES"].DARK;
    // Prevent hydration mismatch
    if (!mounted) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            style: {
                visibility: 'hidden'
            },
            children: children
        }, void 0, false, {
            fileName: "[project]/src/contexts/ThemeContext.tsx",
            lineNumber: 76,
            columnNumber: 12
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ThemeContext.Provider, {
        value: {
            theme,
            isDarkMode,
            toggleTheme,
            setTheme
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/ThemeContext.tsx",
        lineNumber: 80,
        columnNumber: 5
    }, this);
}
_s1(ThemeProvider, "HAoRSwkKNEoONVgbYDoSlBWuQIo=");
_c = ThemeProvider;
var _c;
__turbopack_context__.k.register(_c, "ThemeProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/SettingsContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "SettingsProvider": ()=>SettingsProvider,
    "useSettings": ()=>useSettings
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/index.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
const SettingsContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function useSettings() {
    _s();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(SettingsContext);
    if (context === undefined) {
        throw new Error('useSettings must be used within a SettingsProvider');
    }
    return context;
}
_s(useSettings, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function SettingsProvider(param) {
    let { children } = param;
    _s1();
    const [settings, setSettings] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_SETTINGS"]);
    const [mounted, setMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Load settings from localStorage on mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SettingsProvider.useEffect": ()=>{
            try {
                const savedSettings = localStorage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LOCAL_STORAGE_KEYS"].SETTINGS);
                if (savedSettings) {
                    const parsedSettings = JSON.parse(savedSettings);
                    setSettings({
                        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_SETTINGS"],
                        ...parsedSettings
                    });
                }
            } catch (error) {
                console.error('Error loading settings from localStorage:', error);
            }
            setMounted(true);
        }
    }["SettingsProvider.useEffect"], []);
    // Save settings to localStorage whenever they change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SettingsProvider.useEffect": ()=>{
            if (!mounted) return;
            try {
                localStorage.setItem(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LOCAL_STORAGE_KEYS"].SETTINGS, JSON.stringify(settings));
            } catch (error) {
                console.error('Error saving settings to localStorage:', error);
            }
        }
    }["SettingsProvider.useEffect"], [
        settings,
        mounted
    ]);
    const updateSettings = (newSettings)=>{
        setSettings((prevSettings)=>({
                ...prevSettings,
                ...newSettings
            }));
    };
    const resetSettings = ()=>{
        setSettings(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DEFAULT_SETTINGS"]);
    };
    // Prevent hydration mismatch
    if (!mounted) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            style: {
                visibility: 'hidden'
            },
            children: children
        }, void 0, false, {
            fileName: "[project]/src/contexts/SettingsContext.tsx",
            lineNumber: 77,
            columnNumber: 12
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SettingsContext.Provider, {
        value: {
            settings,
            updateSettings,
            resetSettings
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/SettingsContext.tsx",
        lineNumber: 81,
        columnNumber: 5
    }, this);
}
_s1(SettingsProvider, "yUoYyBUe/3Rss05ZcM+lYWhlU3I=");
_c = SettingsProvider;
var _c;
__turbopack_context__.k.register(_c, "SettingsProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/Providers.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>Providers
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$swr$2f$dist$2f$index$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/swr/dist/index/index.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ThemeContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/ThemeContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$SettingsContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/SettingsContext.tsx [app-client] (ecmascript)");
'use client';
;
;
;
;
function Providers(param) {
    let { children } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ThemeContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ThemeProvider"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$SettingsContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SettingsProvider"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$swr$2f$dist$2f$index$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SWRConfig"], {
                value: {
                    refreshInterval: 30000,
                    revalidateOnFocus: true,
                    revalidateOnReconnect: true,
                    dedupingInterval: 10000,
                    errorRetryCount: 3,
                    errorRetryInterval: 5000,
                    onError: (error)=>{
                        console.error('SWR Error:', error);
                    }
                },
                children: children
            }, void 0, false, {
                fileName: "[project]/src/components/Providers.tsx",
                lineNumber: 16,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/Providers.tsx",
            lineNumber: 15,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/Providers.tsx",
        lineNumber: 14,
        columnNumber: 5
    }, this);
}
_c = Providers;
var _c;
__turbopack_context__.k.register(_c, "Providers");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_1c681469._.js.map