{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/hdcode.dev-crypto-bubble/src/constants/index.ts"], "sourcesContent": ["import { VisualizationMode } from '@/types/crypto';\n\nexport const VISUALIZATION_MODES: VisualizationMode[] = [\n  {\n    id: 'bubble',\n    name: 'Bubble Chart',\n    description: 'Interactive bubble chart where size represents market cap and color represents price change',\n  },\n  {\n    id: 'treemap',\n    name: 'Treemap',\n    description: 'Hierarchical visualization showing market cap proportions',\n  },\n  {\n    id: 'scatter',\n    name: 'Scatter Plot',\n    description: 'Scatter plot showing price vs volume relationships',\n  },\n  {\n    id: 'heatmap',\n    name: 'Heatmap',\n    description: 'Grid-based heatmap showing price changes across different time periods',\n  },\n];\n\nexport const SUPPORTED_CURRENCIES = [\n  { code: 'usd', name: 'US Dollar', symbol: '$' },\n  { code: 'eur', name: 'Euro', symbol: '€' },\n  { code: 'gbp', name: 'British Pound', symbol: '£' },\n  { code: 'jpy', name: 'Japanese Yen', symbol: '¥' },\n  { code: 'btc', name: 'Bitcoin', symbol: '₿' },\n  { code: 'eth', name: 'Ethereum', symbol: 'Ξ' },\n];\n\nexport const TIME_PERIODS = [\n  { value: 1, label: '24H', days: 1 },\n  { value: 7, label: '7D', days: 7 },\n  { value: 30, label: '30D', days: 30 },\n  { value: 90, label: '90D', days: 90 },\n  { value: 365, label: '1Y', days: 365 },\n];\n\nexport const MARKET_CAP_RANGES = [\n  { label: 'All', min: 0, max: Infinity },\n  { label: 'Large Cap (>$10B)', min: 10e9, max: Infinity },\n  { label: 'Mid Cap ($1B-$10B)', min: 1e9, max: 10e9 },\n  { label: 'Small Cap ($100M-$1B)', min: 100e6, max: 1e9 },\n  { label: 'Micro Cap (<$100M)', min: 0, max: 100e6 },\n];\n\nexport const VOLUME_RANGES = [\n  { label: 'All', min: 0, max: Infinity },\n  { label: 'High Volume (>$1B)', min: 1e9, max: Infinity },\n  { label: 'Medium Volume ($100M-$1B)', min: 100e6, max: 1e9 },\n  { label: 'Low Volume (<$100M)', min: 0, max: 100e6 },\n];\n\nexport const PRICE_CHANGE_RANGES = [\n  { label: 'All', min: -Infinity, max: Infinity },\n  { label: 'Strong Gainers (>+10%)', min: 10, max: Infinity },\n  { label: 'Gainers (+5% to +10%)', min: 5, max: 10 },\n  { label: 'Moderate (+0% to +5%)', min: 0, max: 5 },\n  { label: 'Moderate (-5% to 0%)', min: -5, max: 0 },\n  { label: 'Losers (-10% to -5%)', min: -10, max: -5 },\n  { label: 'Strong Losers (<-10%)', min: -Infinity, max: -10 },\n];\n\nexport const BUBBLE_CONFIG = {\n  MIN_RADIUS: 8,\n  MAX_RADIUS: 80,\n  PADDING: 2,\n  ANIMATION_DURATION: 750,\n  COLLISION_STRENGTH: 0.7,\n  CHARGE_STRENGTH: -30,\n};\n\nexport const COLORS = {\n  POSITIVE: {\n    STRONG: '#00C851',\n    MEDIUM: '#00FF41',\n    LIGHT: '#4CAF50',\n  },\n  NEGATIVE: {\n    STRONG: '#FF1744',\n    MEDIUM: '#FF5722',\n    LIGHT: '#F44336',\n  },\n  NEUTRAL: '#9E9E9E',\n  BACKGROUND: {\n    LIGHT: '#FFFFFF',\n    DARK: '#1A1A1A',\n  },\n  TEXT: {\n    PRIMARY_LIGHT: '#000000',\n    PRIMARY_DARK: '#FFFFFF',\n    SECONDARY_LIGHT: '#666666',\n    SECONDARY_DARK: '#CCCCCC',\n  },\n};\n\nexport const THEMES = {\n  LIGHT: 'light',\n  DARK: 'dark',\n};\n\nexport const DEFAULT_SETTINGS = {\n  currency: 'usd',\n  theme: THEMES.LIGHT,\n  refreshInterval: 30000,\n  itemsPerPage: 100,\n  visualizationMode: 'bubble' as const,\n  showLabels: true,\n  showTooltips: true,\n  enableAnimations: true,\n};\n\nexport const API_ENDPOINTS = {\n  COINGECKO_BASE: 'https://api.coingecko.com/api/v3',\n  RATE_LIMIT_DELAY: 100, // milliseconds\n  TIMEOUT: 10000, // milliseconds\n};\n\nexport const LOCAL_STORAGE_KEYS = {\n  THEME: 'crypto-bubble-theme',\n  CURRENCY: 'crypto-bubble-currency',\n  SETTINGS: 'crypto-bubble-settings',\n  PORTFOLIO: 'crypto-bubble-portfolio',\n  FAVORITES: 'crypto-bubble-favorites',\n};\n\nexport const SOCIAL_LINKS = {\n  TWITTER: 'https://twitter.com',\n  TELEGRAM: 'https://t.me',\n  REDDIT: 'https://reddit.com',\n  GITHUB: 'https://github.com',\n};\n\nexport const EXPORT_FORMATS = [\n  { value: 'png', label: 'PNG Image' },\n  { value: 'svg', label: 'SVG Vector' },\n  { value: 'pdf', label: 'PDF Document' },\n  { value: 'csv', label: 'CSV Data' },\n  { value: 'json', label: 'JSON Data' },\n];\n\nexport const CHART_DIMENSIONS = {\n  DEFAULT_WIDTH: 800,\n  DEFAULT_HEIGHT: 600,\n  MIN_WIDTH: 400,\n  MIN_HEIGHT: 300,\n  ASPECT_RATIO: 4 / 3,\n};\n\nexport const ANIMATION_PRESETS = {\n  FAST: { duration: 300, easing: 'ease-out' },\n  NORMAL: { duration: 500, easing: 'ease-in-out' },\n  SLOW: { duration: 750, easing: 'ease-in-out' },\n  SPRING: { type: 'spring', stiffness: 300, damping: 30 },\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEO,MAAM,sBAA2C;IACtD;QACE,IAAI;QACJ,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;IACf;CACD;AAEM,MAAM,uBAAuB;IAClC;QAAE,MAAM;QAAO,MAAM;QAAa,QAAQ;IAAI;IAC9C;QAAE,MAAM;QAAO,MAAM;QAAQ,QAAQ;IAAI;IACzC;QAAE,MAAM;QAAO,MAAM;QAAiB,QAAQ;IAAI;IAClD;QAAE,MAAM;QAAO,MAAM;QAAgB,QAAQ;IAAI;IACjD;QAAE,MAAM;QAAO,MAAM;QAAW,QAAQ;IAAI;IAC5C;QAAE,MAAM;QAAO,MAAM;QAAY,QAAQ;IAAI;CAC9C;AAEM,MAAM,eAAe;IAC1B;QAAE,OAAO;QAAG,OAAO;QAAO,MAAM;IAAE;IAClC;QAAE,OAAO;QAAG,OAAO;QAAM,MAAM;IAAE;IACjC;QAAE,OAAO;QAAI,OAAO;QAAO,MAAM;IAAG;IACpC;QAAE,OAAO;QAAI,OAAO;QAAO,MAAM;IAAG;IACpC;QAAE,OAAO;QAAK,OAAO;QAAM,MAAM;IAAI;CACtC;AAEM,MAAM,oBAAoB;IAC/B;QAAE,OAAO;QAAO,KAAK;QAAG,KAAK;IAAS;IACtC;QAAE,OAAO;QAAqB,KAAK;QAAM,KAAK;IAAS;IACvD;QAAE,OAAO;QAAsB,KAAK;QAAK,KAAK;IAAK;IACnD;QAAE,OAAO;QAAyB,KAAK;QAAO,KAAK;IAAI;IACvD;QAAE,OAAO;QAAsB,KAAK;QAAG,KAAK;IAAM;CACnD;AAEM,MAAM,gBAAgB;IAC3B;QAAE,OAAO;QAAO,KAAK;QAAG,KAAK;IAAS;IACtC;QAAE,OAAO;QAAsB,KAAK;QAAK,KAAK;IAAS;IACvD;QAAE,OAAO;QAA6B,KAAK;QAAO,KAAK;IAAI;IAC3D;QAAE,OAAO;QAAuB,KAAK;QAAG,KAAK;IAAM;CACpD;AAEM,MAAM,sBAAsB;IACjC;QAAE,OAAO;QAAO,KAAK,CAAC;QAAU,KAAK;IAAS;IAC9C;QAAE,OAAO;QAA0B,KAAK;QAAI,KAAK;IAAS;IAC1D;QAAE,OAAO;QAAyB,KAAK;QAAG,KAAK;IAAG;IAClD;QAAE,OAAO;QAAyB,KAAK;QAAG,KAAK;IAAE;IACjD;QAAE,OAAO;QAAwB,KAAK,CAAC;QAAG,KAAK;IAAE;IACjD;QAAE,OAAO;QAAwB,KAAK,CAAC;QAAI,KAAK,CAAC;IAAE;IACnD;QAAE,OAAO;QAAyB,KAAK,CAAC;QAAU,KAAK,CAAC;IAAG;CAC5D;AAEM,MAAM,gBAAgB;IAC3B,YAAY;IACZ,YAAY;IACZ,SAAS;IACT,oBAAoB;IACpB,oBAAoB;IACpB,iBAAiB,CAAC;AACpB;AAEO,MAAM,SAAS;IACpB,UAAU;QACR,QAAQ;QACR,QAAQ;QACR,OAAO;IACT;IACA,UAAU;QACR,QAAQ;QACR,QAAQ;QACR,OAAO;IACT;IACA,SAAS;IACT,YAAY;QACV,OAAO;QACP,MAAM;IACR;IACA,MAAM;QACJ,eAAe;QACf,cAAc;QACd,iBAAiB;QACjB,gBAAgB;IAClB;AACF;AAEO,MAAM,SAAS;IACpB,OAAO;IACP,MAAM;AACR;AAEO,MAAM,mBAAmB;IAC9B,UAAU;IACV,OAAO,OAAO,KAAK;IACnB,iBAAiB;IACjB,cAAc;IACd,mBAAmB;IACnB,YAAY;IACZ,cAAc;IACd,kBAAkB;AACpB;AAEO,MAAM,gBAAgB;IAC3B,gBAAgB;IAChB,kBAAkB;IAClB,SAAS;AACX;AAEO,MAAM,qBAAqB;IAChC,OAAO;IACP,UAAU;IACV,UAAU;IACV,WAAW;IACX,WAAW;AACb;AAEO,MAAM,eAAe;IAC1B,SAAS;IACT,UAAU;IACV,QAAQ;IACR,QAAQ;AACV;AAEO,MAAM,iBAAiB;IAC5B;QAAE,OAAO;QAAO,OAAO;IAAY;IACnC;QAAE,OAAO;QAAO,OAAO;IAAa;IACpC;QAAE,OAAO;QAAO,OAAO;IAAe;IACtC;QAAE,OAAO;QAAO,OAAO;IAAW;IAClC;QAAE,OAAO;QAAQ,OAAO;IAAY;CACrC;AAEM,MAAM,mBAAmB;IAC9B,eAAe;IACf,gBAAgB;IAChB,WAAW;IACX,YAAY;IACZ,cAAc,IAAI;AACpB;AAEO,MAAM,oBAAoB;IAC/B,MAAM;QAAE,UAAU;QAAK,QAAQ;IAAW;IAC1C,QAAQ;QAAE,UAAU;QAAK,QAAQ;IAAc;IAC/C,MAAM;QAAE,UAAU;QAAK,QAAQ;IAAc;IAC7C,QAAQ;QAAE,MAAM;QAAU,WAAW;QAAK,SAAS;IAAG;AACxD", "debugId": null}}, {"offset": {"line": 312, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/hdcode.dev-crypto-bubble/src/contexts/ThemeContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { LOCAL_STORAGE_KEYS, THEMES } from '@/constants';\n\ninterface ThemeContextType {\n  theme: string;\n  isDarkMode: boolean;\n  toggleTheme: () => void;\n  setTheme: (theme: string) => void;\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport function useTheme() {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n}\n\ninterface ThemeProviderProps {\n  children: React.ReactNode;\n}\n\nexport function ThemeProvider({ children }: ThemeProviderProps) {\n  const [theme, setThemeState] = useState<string>(THEMES.LIGHT);\n  const [mounted, setMounted] = useState(false);\n\n  // Initialize theme from localStorage or system preference\n  useEffect(() => {\n    const savedTheme = localStorage.getItem(LOCAL_STORAGE_KEYS.THEME);\n    if (savedTheme && (savedTheme === THEMES.LIGHT || savedTheme === THEMES.DARK)) {\n      setThemeState(savedTheme);\n    } else {\n      // Check system preference\n      const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n      setThemeState(systemPrefersDark ? THEMES.DARK : THEMES.LIGHT);\n    }\n    setMounted(true);\n  }, []);\n\n  // Apply theme to document\n  useEffect(() => {\n    if (!mounted) return;\n\n    const root = document.documentElement;\n\n    if (theme === THEMES.DARK) {\n      root.classList.add('dark');\n    } else {\n      root.classList.remove('dark');\n    }\n\n    // Save to localStorage\n    localStorage.setItem(LOCAL_STORAGE_KEYS.THEME, theme);\n  }, [theme, mounted]);\n\n  const toggleTheme = () => {\n    setThemeState(prevTheme =>\n      prevTheme === THEMES.LIGHT ? THEMES.DARK : THEMES.LIGHT\n    );\n  };\n\n  const setTheme = (newTheme: string) => {\n    if (newTheme === THEMES.LIGHT || newTheme === THEMES.DARK) {\n      setThemeState(newTheme);\n    }\n  };\n\n  const isDarkMode = theme === THEMES.DARK;\n\n  // Always provide the context, but hide content during hydration\n  return (\n    <ThemeContext.Provider value={{ theme, isDarkMode, toggleTheme, setTheme }}>\n      {!mounted ? (\n        <div style={{ visibility: 'hidden' }}>{children}</div>\n      ) : (\n        children\n      )}\n    </ThemeContext.Provider>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAYA,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANgB;AAYT,SAAS,cAAc,KAAgC;QAAhC,EAAE,QAAQ,EAAsB,GAAhC;;IAC5B,MAAM,CAAC,OAAO,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,4HAAA,CAAA,SAAM,CAAC,KAAK;IAC5D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,0DAA0D;IAC1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,aAAa,aAAa,OAAO,CAAC,4HAAA,CAAA,qBAAkB,CAAC,KAAK;YAChE,IAAI,cAAc,CAAC,eAAe,4HAAA,CAAA,SAAM,CAAC,KAAK,IAAI,eAAe,4HAAA,CAAA,SAAM,CAAC,IAAI,GAAG;gBAC7E,cAAc;YAChB,OAAO;gBACL,0BAA0B;gBAC1B,MAAM,oBAAoB,OAAO,UAAU,CAAC,gCAAgC,OAAO;gBACnF,cAAc,oBAAoB,4HAAA,CAAA,SAAM,CAAC,IAAI,GAAG,4HAAA,CAAA,SAAM,CAAC,KAAK;YAC9D;YACA,WAAW;QACb;kCAAG,EAAE;IAEL,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,SAAS;YAEd,MAAM,OAAO,SAAS,eAAe;YAErC,IAAI,UAAU,4HAAA,CAAA,SAAM,CAAC,IAAI,EAAE;gBACzB,KAAK,SAAS,CAAC,GAAG,CAAC;YACrB,OAAO;gBACL,KAAK,SAAS,CAAC,MAAM,CAAC;YACxB;YAEA,uBAAuB;YACvB,aAAa,OAAO,CAAC,4HAAA,CAAA,qBAAkB,CAAC,KAAK,EAAE;QACjD;kCAAG;QAAC;QAAO;KAAQ;IAEnB,MAAM,cAAc;QAClB,cAAc,CAAA,YACZ,cAAc,4HAAA,CAAA,SAAM,CAAC,KAAK,GAAG,4HAAA,CAAA,SAAM,CAAC,IAAI,GAAG,4HAAA,CAAA,SAAM,CAAC,KAAK;IAE3D;IAEA,MAAM,WAAW,CAAC;QAChB,IAAI,aAAa,4HAAA,CAAA,SAAM,CAAC,KAAK,IAAI,aAAa,4HAAA,CAAA,SAAM,CAAC,IAAI,EAAE;YACzD,cAAc;QAChB;IACF;IAEA,MAAM,aAAa,UAAU,4HAAA,CAAA,SAAM,CAAC,IAAI;IAExC,gEAAgE;IAChE,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAO;YAAY;YAAa;QAAS;kBACtE,CAAC,wBACA,6LAAC;YAAI,OAAO;gBAAE,YAAY;YAAS;sBAAI;;;;;mBAEvC;;;;;;AAIR;IAzDgB;KAAA", "debugId": null}}, {"offset": {"line": 416, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/hdcode.dev-crypto-bubble/src/contexts/SettingsContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { LOCAL_STORAGE_KEYS, DEFAULT_SETTINGS } from '@/constants';\nimport { VisualizationMode } from '@/types/crypto';\n\ninterface Settings {\n  currency: string;\n  refreshInterval: number;\n  itemsPerPage: number;\n  visualizationMode: VisualizationMode['id'];\n  showLabels: boolean;\n  showTooltips: boolean;\n  enableAnimations: boolean;\n  autoRefresh: boolean;\n}\n\ninterface SettingsContextType {\n  settings: Settings;\n  updateSettings: (newSettings: Partial<Settings>) => void;\n  resetSettings: () => void;\n}\n\nconst SettingsContext = createContext<SettingsContextType | undefined>(undefined);\n\nexport function useSettings() {\n  const context = useContext(SettingsContext);\n  if (context === undefined) {\n    throw new Error('useSettings must be used within a SettingsProvider');\n  }\n  return context;\n}\n\ninterface SettingsProviderProps {\n  children: React.ReactNode;\n}\n\nexport function SettingsProvider({ children }: SettingsProviderProps) {\n  const [settings, setSettings] = useState<Settings>(DEFAULT_SETTINGS);\n  const [mounted, setMounted] = useState(false);\n\n  // Load settings from localStorage on mount\n  useEffect(() => {\n    try {\n      const savedSettings = localStorage.getItem(LOCAL_STORAGE_KEYS.SETTINGS);\n      if (savedSettings) {\n        const parsedSettings = JSON.parse(savedSettings);\n        setSettings({ ...DEFAULT_SETTINGS, ...parsedSettings });\n      }\n    } catch (error) {\n      console.error('Error loading settings from localStorage:', error);\n    }\n    setMounted(true);\n  }, []);\n\n  // Save settings to localStorage whenever they change\n  useEffect(() => {\n    if (!mounted) return;\n    \n    try {\n      localStorage.setItem(LOCAL_STORAGE_KEYS.SETTINGS, JSON.stringify(settings));\n    } catch (error) {\n      console.error('Error saving settings to localStorage:', error);\n    }\n  }, [settings, mounted]);\n\n  const updateSettings = (newSettings: Partial<Settings>) => {\n    setSettings(prevSettings => ({ ...prevSettings, ...newSettings }));\n  };\n\n  const resetSettings = () => {\n    setSettings(DEFAULT_SETTINGS);\n  };\n\n  // Always provide the context, but hide content during hydration\n  return (\n    <SettingsContext.Provider value={{ settings, updateSettings, resetSettings }}>\n      {!mounted ? (\n        <div style={{ visibility: 'hidden' }}>{children}</div>\n      ) : (\n        children\n      )}\n    </SettingsContext.Provider>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAuBA,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAmC;AAEhE,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANgB;AAYT,SAAS,iBAAiB,KAAmC;QAAnC,EAAE,QAAQ,EAAyB,GAAnC;;IAC/B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,4HAAA,CAAA,mBAAgB;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI;gBACF,MAAM,gBAAgB,aAAa,OAAO,CAAC,4HAAA,CAAA,qBAAkB,CAAC,QAAQ;gBACtE,IAAI,eAAe;oBACjB,MAAM,iBAAiB,KAAK,KAAK,CAAC;oBAClC,YAAY;wBAAE,GAAG,4HAAA,CAAA,mBAAgB;wBAAE,GAAG,cAAc;oBAAC;gBACvD;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6CAA6C;YAC7D;YACA,WAAW;QACb;qCAAG,EAAE;IAEL,qDAAqD;IACrD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,CAAC,SAAS;YAEd,IAAI;gBACF,aAAa,OAAO,CAAC,4HAAA,CAAA,qBAAkB,CAAC,QAAQ,EAAE,KAAK,SAAS,CAAC;YACnE,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0CAA0C;YAC1D;QACF;qCAAG;QAAC;QAAU;KAAQ;IAEtB,MAAM,iBAAiB,CAAC;QACtB,YAAY,CAAA,eAAgB,CAAC;gBAAE,GAAG,YAAY;gBAAE,GAAG,WAAW;YAAC,CAAC;IAClE;IAEA,MAAM,gBAAgB;QACpB,YAAY,4HAAA,CAAA,mBAAgB;IAC9B;IAEA,gEAAgE;IAChE,qBACE,6LAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;YAAU;YAAgB;QAAc;kBACxE,CAAC,wBACA,6LAAC;YAAI,OAAO;gBAAE,YAAY;YAAS;sBAAI;;;;;mBAEvC;;;;;;AAIR;IA/CgB;KAAA", "debugId": null}}, {"offset": {"line": 520, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/hdcode.dev-crypto-bubble/src/components/Providers.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { SWRConfig } from 'swr';\nimport { ThemeProvider } from '@/contexts/ThemeContext';\nimport { SettingsProvider } from '@/contexts/SettingsContext';\n\ninterface ProvidersProps {\n  children: React.ReactNode;\n}\n\nexport default function Providers({ children }: ProvidersProps) {\n  return (\n    <ThemeProvider>\n      <SettingsProvider>\n        <SWRConfig\n          value={{\n            refreshInterval: 30000, // Refresh every 30 seconds\n            revalidateOnFocus: true,\n            revalidateOnReconnect: true,\n            dedupingInterval: 10000, // Dedupe requests within 10 seconds\n            errorRetryCount: 3,\n            errorRetryInterval: 5000,\n            onError: (error) => {\n              console.error('SWR Error:', error);\n            },\n          }}\n        >\n          {children}\n        </SWRConfig>\n      </SettingsProvider>\n    </ThemeProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAWe,SAAS,UAAU,KAA4B;QAA5B,EAAE,QAAQ,EAAkB,GAA5B;IAChC,qBACE,6LAAC,mIAAA,CAAA,gBAAa;kBACZ,cAAA,6LAAC,sIAAA,CAAA,mBAAgB;sBACf,cAAA,6LAAC,iKAAA,CAAA,YAAS;gBACR,OAAO;oBACL,iBAAiB;oBACjB,mBAAmB;oBACnB,uBAAuB;oBACvB,kBAAkB;oBAClB,iBAAiB;oBACjB,oBAAoB;oBACpB,SAAS,CAAC;wBACR,QAAQ,KAAK,CAAC,cAAc;oBAC9B;gBACF;0BAEC;;;;;;;;;;;;;;;;AAKX;KAtBwB", "debugId": null}}]}