{"version": 3, "sources": [], "sections": [{"offset": {"line": 9, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/hdcode.dev-crypto-bubble/node_modules/mime-db/index.js"], "sourcesContent": ["/*!\n * mime-db\n * Copyright(c) 2014 <PERSON>\n * Copyright(c) 2015-2022 <PERSON>\n * MIT Licensed\n */\n\n/**\n * Module exports.\n */\n\nmodule.exports = require('./db.json')\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED;;CAEC,GAED,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}