/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "m14 6 4 4", key: "1q72g9" }],
  ["path", { d: "M17 3h4v4", key: "19p9u1" }],
  ["path", { d: "m21 3-7.75 7.75", key: "1cjbfd" }],
  ["circle", { cx: "9", cy: "15", r: "6", key: "bx5svt" }]
];
const MarsStroke = createLucideIcon("mars-stroke", __iconNode);

export { __iconNode, MarsStroke as default };
//# sourceMappingURL=mars-stroke.js.map
