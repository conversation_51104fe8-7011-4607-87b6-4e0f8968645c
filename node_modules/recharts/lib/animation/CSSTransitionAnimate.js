"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.CSSTransitionAnimate = CSSTransitionAnimate;
var _react = require("react");
var _esToolkit = require("es-toolkit");
var _resolveDefaultProps = require("../util/resolveDefaultProps");
var _useAnimationManager = require("./useAnimationManager");
var _util = require("./util");
var defaultProps = {
  begin: 0,
  duration: 1000,
  easing: 'ease',
  isActive: true,
  canBegin: true,
  onAnimationEnd: () => {},
  onAnimationStart: () => {}
};
function CSSTransitionAnimate(outsideProps) {
  var props = (0, _resolveDefaultProps.resolveDefaultProps)(outsideProps, defaultProps);
  var {
    from,
    to,
    attributeName,
    isActive,
    canBegin,
    duration,
    easing,
    begin,
    onAnimationEnd,
    onAnimationStart,
    children
  } = props;
  var animationManager = (0, _useAnimationManager.useAnimationManager)(attributeName, props.animationManager);
  var [style, setStyle] = (0, _react.useState)(isActive ? from : to);
  (0, _react.useEffect)(() => {
    if (!isActive) {
      setStyle(to);
    }
  }, [isActive, to]);
  (0, _react.useEffect)(() => {
    if (!isActive || !canBegin) {
      return _esToolkit.noop;
    }
    var unsubscribe = animationManager.subscribe(setStyle);
    animationManager.start([onAnimationStart, begin, to, duration, onAnimationEnd]);
    return () => {
      animationManager.stop();
      if (unsubscribe) {
        unsubscribe();
      }
      onAnimationEnd();
    };
  }, [isActive, canBegin, duration, easing, begin, onAnimationStart, onAnimationEnd, animationManager, to]);
  if (isActive && canBegin) {
    var transition = (0, _util.getTransitionVal)([attributeName], duration, easing);
    return children({
      transition,
      [attributeName]: style
    });
  }
  return children({
    [attributeName]: style
  });
}