import { useEffect, useState } from 'react';
import { noop } from 'es-toolkit';
import { resolveDefaultProps } from '../util/resolveDefaultProps';
import { useAnimationManager } from './useAnimationManager';
import { getTransitionVal } from './util';
var defaultProps = {
  begin: 0,
  duration: 1000,
  easing: 'ease',
  isActive: true,
  canBegin: true,
  onAnimationEnd: () => {},
  onAnimationStart: () => {}
};
export function CSSTransitionAnimate(outsideProps) {
  var props = resolveDefaultProps(outsideProps, defaultProps);
  var {
    from,
    to,
    attributeName,
    isActive,
    canBegin,
    duration,
    easing,
    begin,
    onAnimationEnd,
    onAnimationStart,
    children
  } = props;
  var animationManager = useAnimationManager(attributeName, props.animationManager);
  var [style, setStyle] = useState(isActive ? from : to);
  useEffect(() => {
    if (!isActive) {
      setStyle(to);
    }
  }, [isActive, to]);
  useEffect(() => {
    if (!isActive || !canBegin) {
      return noop;
    }
    var unsubscribe = animationManager.subscribe(setStyle);
    animationManager.start([onAnimationStart, begin, to, duration, onAnimationEnd]);
    return () => {
      animationManager.stop();
      if (unsubscribe) {
        unsubscribe();
      }
      onAnimationEnd();
    };
  }, [isActive, canBegin, duration, easing, begin, onAnimationStart, onAnimationEnd, animationManager, to]);
  if (isActive && canBegin) {
    var transition = getTransitionVal([attributeName], duration, easing);
    return children({
      transition,
      [attributeName]: style
    });
  }
  return children({
    [attributeName]: style
  });
}