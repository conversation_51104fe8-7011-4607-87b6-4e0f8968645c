'use client';

import React from 'react';
import { SWRConfig } from 'swr';
import { ThemeProvider } from '@/contexts/ThemeContext';
import { SettingsProvider } from '@/contexts/SettingsContext';

interface ProvidersProps {
  children: React.ReactNode;
}

export default function Providers({ children }: ProvidersProps) {
  return (
    <ThemeProvider>
      <SettingsProvider>
        <SWRConfig
          value={{
            refreshInterval: 30000, // Refresh every 30 seconds
            revalidateOnFocus: true,
            revalidateOnReconnect: true,
            dedupingInterval: 10000, // Dedupe requests within 10 seconds
            errorRetryCount: 3,
            errorRetryInterval: 5000,
            onError: (error) => {
              console.error('SWR Error:', error);
            },
          }}
        >
          {children}
        </SWRConfig>
      </SettingsProvider>
    </ThemeProvider>
  );
}
