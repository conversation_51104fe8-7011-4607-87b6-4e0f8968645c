'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Search, X, TrendingUp, TrendingDown, Star } from 'lucide-react';
import { useSearchCryptocurrencies, useTrendingCryptocurrencies } from '@/hooks/useCrypto';
import { debounce, formatCurrency, formatPercentage } from '@/utils/dataTransform';

interface AdvancedSearchProps {
  onCryptoSelect?: (crypto: any) => void;
  onAddToWatchlist?: (crypto: any) => void;
  placeholder?: string;
  className?: string;
}

export default function AdvancedSearch({ 
  onCryptoSelect, 
  onAddToWatchlist,
  placeholder = "Search cryptocurrencies...",
  className = ""
}: AdvancedSearchProps) {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [debouncedQuery, setDebouncedQuery] = useState('');
  const [activeTab, setActiveTab] = useState<'search' | 'trending'>('search');
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const { searchResults, isLoading: searchLoading } = useSearchCryptocurrencies(debouncedQuery);
  const { trending, isLoading: trendingLoading } = useTrendingCryptocurrencies();

  // Debounce search query
  const debouncedSetQuery = debounce((value: string) => {
    setDebouncedQuery(value);
  }, 300);

  useEffect(() => {
    debouncedSetQuery(query);
  }, [query, debouncedSetQuery]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !inputRef.current?.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    setIsOpen(true);
    setActiveTab('search');
  };

  const handleInputFocus = () => {
    setIsOpen(true);
    if (query.length === 0) {
      setActiveTab('trending');
    }
  };

  const handleCryptoSelect = (crypto: any) => {
    setQuery('');
    setDebouncedQuery('');
    setIsOpen(false);
    onCryptoSelect?.(crypto);
  };

  const handleAddToWatchlist = (crypto: any, event: React.MouseEvent) => {
    event.stopPropagation();
    onAddToWatchlist?.(crypto);
  };

  const clearSearch = () => {
    setQuery('');
    setDebouncedQuery('');
    setIsOpen(false);
    inputRef.current?.focus();
  };

  const renderSearchResults = () => {
    if (searchLoading) {
      return (
        <div className="px-4 py-3 text-sm text-gray-500 dark:text-gray-400">
          Searching...
        </div>
      );
    }

    if (searchResults.length === 0 && debouncedQuery.length > 2) {
      return (
        <div className="px-4 py-3 text-sm text-gray-500 dark:text-gray-400">
          No cryptocurrencies found for "{debouncedQuery}"
        </div>
      );
    }

    return (
      <div className="py-1">
        {searchResults.slice(0, 8).map((crypto) => (
          <div
            key={crypto.id}
            className="px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer transition-colors"
            onClick={() => handleCryptoSelect(crypto)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3 flex-1 min-w-0">
                <img
                  src={crypto.large || crypto.thumb}
                  alt={crypto.name}
                  className="w-8 h-8 rounded-full flex-shrink-0"
                />
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {crypto.name}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-400 uppercase">
                      {crypto.symbol}
                    </span>
                  </div>
                  {crypto.market_cap_rank && (
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Rank #{crypto.market_cap_rank}
                    </div>
                  )}
                </div>
              </div>
              <button
                onClick={(e) => handleAddToWatchlist(crypto, e)}
                className="p-1 hover:bg-gray-200 dark:hover:bg-gray-500 rounded transition-colors"
                title="Add to watchlist"
              >
                <Star className="w-4 h-4 text-gray-400 hover:text-yellow-500" />
              </button>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderTrendingResults = () => {
    if (trendingLoading) {
      return (
        <div className="px-4 py-3 text-sm text-gray-500 dark:text-gray-400">
          Loading trending...
        </div>
      );
    }

    return (
      <div className="py-1">
        <div className="px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
          Trending Now
        </div>
        {trending.slice(0, 6).map((item) => {
          const crypto = item.item;
          return (
            <div
              key={crypto.id}
              className="px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer transition-colors"
              onClick={() => handleCryptoSelect(crypto)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3 flex-1 min-w-0">
                  <img
                    src={crypto.large || crypto.thumb}
                    alt={crypto.name}
                    className="w-8 h-8 rounded-full flex-shrink-0"
                  />
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {crypto.name}
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400 uppercase">
                        {crypto.symbol}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        Rank #{crypto.market_cap_rank}
                      </span>
                      <div className="flex items-center space-x-1">
                        <TrendingUp className="w-3 h-3 text-orange-500" />
                        <span className="text-xs text-orange-500 font-medium">
                          Trending
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  onClick={(e) => handleAddToWatchlist(crypto, e)}
                  className="p-1 hover:bg-gray-200 dark:hover:bg-gray-500 rounded transition-colors"
                  title="Add to watchlist"
                >
                  <Star className="w-4 h-4 text-gray-400 hover:text-yellow-500" />
                </button>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className={`relative ${className}`}>
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          placeholder={placeholder}
          className="block w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        {query && (
          <button
            onClick={clearSearch}
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
          >
            <X className="h-5 w-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" />
          </button>
        )}
      </div>

      {/* Search Results Dropdown */}
      {isOpen && (
        <div
          ref={dropdownRef}
          className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg max-h-80 overflow-hidden"
        >
          {/* Tabs */}
          <div className="flex border-b border-gray-200 dark:border-gray-600">
            <button
              onClick={() => setActiveTab('search')}
              className={`flex-1 px-4 py-2 text-sm font-medium transition-colors ${
                activeTab === 'search'
                  ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'
                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
            >
              Search Results
            </button>
            <button
              onClick={() => setActiveTab('trending')}
              className={`flex-1 px-4 py-2 text-sm font-medium transition-colors ${
                activeTab === 'trending'
                  ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'
                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
            >
              Trending
            </button>
          </div>

          {/* Content */}
          <div className="max-h-64 overflow-y-auto">
            {activeTab === 'search' ? renderSearchResults() : renderTrendingResults()}
          </div>
        </div>
      )}
    </div>
  );
}
