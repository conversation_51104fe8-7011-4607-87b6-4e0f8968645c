'use client';

import React, { useEffect, useRef, useState } from 'react';
import * as d3 from 'd3';
import { BubbleData } from '@/types/crypto';
import { calculateBubbleRadius, getPriceChangeColor, formatCurrency, formatPercentage } from '@/utils/dataTransform';
import { BUBBLE_CONFIG, COLORS } from '@/constants';

interface BubbleChartProps {
  data: BubbleData[];
  width?: number;
  height?: number;
  onBubbleClick?: (data: BubbleData) => void;
  onBubbleHover?: (data: BubbleData | null) => void;
  className?: string;
}

interface TooltipData {
  data: BubbleData;
  x: number;
  y: number;
}

export default function BubbleChart({
  data,
  width = 800,
  height = 600,
  onBubbleClick,
  onBubbleHover,
  className = '',
}: BubbleChartProps) {
  const svgRef = useRef<SVGSVGElement>(null);
  const [tooltip, setTooltip] = useState<TooltipData | null>(null);

  useEffect(() => {
    if (!data.length || !svgRef.current) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    // Calculate bubble radii
    const marketCaps = data.map(d => d.value);
    const bubbleData = data.map(d => ({
      ...d,
      radius: calculateBubbleRadius(d.value, BUBBLE_CONFIG.MIN_RADIUS, BUBBLE_CONFIG.MAX_RADIUS, marketCaps),
    }));

    // Create simulation
    const simulation = d3.forceSimulation(bubbleData as any)
      .force('charge', d3.forceManyBody().strength(BUBBLE_CONFIG.CHARGE_STRENGTH))
      .force('center', d3.forceCenter(width / 2, height / 2))
      .force('collision', d3.forceCollide().radius((d: any) => d.radius + BUBBLE_CONFIG.PADDING).strength(BUBBLE_CONFIG.COLLISION_STRENGTH));

    // Create bubbles
    const bubbles = svg
      .selectAll('circle')
      .data(bubbleData)
      .enter()
      .append('circle')
      .attr('r', 0)
      .attr('fill', d => getPriceChangeColor(d.change))
      .attr('stroke', '#fff')
      .attr('stroke-width', 1)
      .style('cursor', 'pointer')
      .style('opacity', 0.8);

    // Create labels
    const labels = svg
      .selectAll('text')
      .data(bubbleData.filter(d => d.radius > 20)) // Only show labels for larger bubbles
      .enter()
      .append('text')
      .attr('text-anchor', 'middle')
      .attr('dy', '0.3em')
      .style('font-size', d => `${Math.min(d.radius / 3, 14)}px`)
      .style('font-weight', 'bold')
      .style('fill', '#fff')
      .style('pointer-events', 'none')
      .text(d => d.symbol);

    // Animation
    bubbles
      .transition()
      .duration(BUBBLE_CONFIG.ANIMATION_DURATION)
      .attr('r', d => d.radius)
      .style('opacity', 0.8);

    // Event handlers
    bubbles
      .on('mouseover', function(event, d) {
        d3.select(this)
          .transition()
          .duration(200)
          .style('opacity', 1)
          .attr('stroke-width', 2);

        const [x, y] = d3.pointer(event, svgRef.current);
        setTooltip({ data: d, x, y });
        onBubbleHover?.(d);
      })
      .on('mouseout', function(event, d) {
        d3.select(this)
          .transition()
          .duration(200)
          .style('opacity', 0.8)
          .attr('stroke-width', 1);

        setTooltip(null);
        onBubbleHover?.(null);
      })
      .on('click', function(event, d) {
        onBubbleClick?.(d);
      });

    // Update positions on simulation tick
    simulation.on('tick', () => {
      bubbles
        .attr('cx', (d: any) => d.x)
        .attr('cy', (d: any) => d.y);

      labels
        .attr('x', (d: any) => d.x)
        .attr('y', (d: any) => d.y);
    });

    // Cleanup
    return () => {
      simulation.stop();
    };
  }, [data, width, height, onBubbleClick, onBubbleHover]);

  return (
    <div className={`relative ${className}`}>
      <svg
        ref={svgRef}
        width={width}
        height={height}
        className="border border-gray-200 rounded-lg bg-white dark:bg-gray-900 dark:border-gray-700"
      />
      
      {tooltip && (
        <div
          className="absolute z-10 bg-black bg-opacity-90 text-white p-3 rounded-lg shadow-lg pointer-events-none"
          style={{
            left: tooltip.x + 10,
            top: tooltip.y - 10,
            transform: tooltip.x > width - 200 ? 'translateX(-100%)' : 'none',
          }}
        >
          <div className="font-bold text-lg">{tooltip.data.name}</div>
          <div className="text-sm text-gray-300">{tooltip.data.symbol}</div>
          <div className="mt-2 space-y-1">
            <div>Price: {formatCurrency(tooltip.data.price)}</div>
            <div>Market Cap: {formatCurrency(tooltip.data.value)}</div>
            <div>Volume: {formatCurrency(tooltip.data.volume)}</div>
            <div className={`${tooltip.data.change >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              24h Change: {formatPercentage(tooltip.data.change)}
            </div>
            <div className="text-xs text-gray-400">Rank #{tooltip.data.rank}</div>
          </div>
        </div>
      )}
    </div>
  );
}
