'use client';

import React from 'react';
import B<PERSON>ble<PERSON>hart from './BubbleChart';
import Treemap<PERSON>hart from './TreemapChart';
import { BubbleData, VisualizationMode } from '@/types/crypto';
import { BubbleChartSkeleton } from './LoadingSpinner';

interface ChartContainerProps {
  data: BubbleData[];
  mode: VisualizationMode['id'];
  width?: number;
  height?: number;
  isLoading?: boolean;
  onItemClick?: (data: BubbleData) => void;
  onItemHover?: (data: BubbleData | null) => void;
  className?: string;
}

export default function ChartContainer({
  data,
  mode,
  width = 800,
  height = 600,
  isLoading = false,
  onItemClick,
  onItemHover,
  className = '',
}: ChartContainerProps) {
  if (isLoading) {
    return <BubbleChartSkeleton width={width} height={height} />;
  }

  const renderChart = () => {
    switch (mode) {
      case 'bubble':
        return (
          <BubbleChart
            data={data}
            width={width}
            height={height}
            onBubbleClick={onItemClick}
            onBubbleHover={onItemHover}
            className={className}
          />
        );
      
      case 'treemap':
        return (
          <TreemapChart
            data={data}
            width={width}
            height={height}
            onCellClick={onItemClick}
            onCellHover={onItemHover}
            className={className}
          />
        );
      
      case 'scatter':
        return (
          <div 
            className={`border border-gray-200 rounded-lg bg-gray-50 dark:bg-gray-800 dark:border-gray-700 flex items-center justify-center ${className}`}
            style={{ width, height }}
          >
            <div className="text-center">
              <div className="text-gray-500 dark:text-gray-400 mb-2">
                <svg className="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 001.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Scatter Plot
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Coming soon! This will show price vs volume relationships.
              </p>
            </div>
          </div>
        );
      
      case 'heatmap':
        return (
          <div 
            className={`border border-gray-200 rounded-lg bg-gray-50 dark:bg-gray-800 dark:border-gray-700 flex items-center justify-center ${className}`}
            style={{ width, height }}
          >
            <div className="text-center">
              <div className="text-gray-500 dark:text-gray-400 mb-2">
                <svg className="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Heatmap
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Coming soon! This will show price changes across different time periods.
              </p>
            </div>
          </div>
        );
      
      default:
        return (
          <BubbleChart
            data={data}
            width={width}
            height={height}
            onBubbleClick={onItemClick}
            onBubbleHover={onItemHover}
            className={className}
          />
        );
    }
  };

  return (
    <div className="relative">
      {renderChart()}
      
      {/* Mode indicator */}
      <div className="absolute top-4 right-4 bg-white dark:bg-gray-800 px-3 py-1 rounded-full shadow-sm border border-gray-200 dark:border-gray-700">
        <span className="text-xs font-medium text-gray-600 dark:text-gray-400 capitalize">
          {mode} View
        </span>
      </div>
    </div>
  );
}
