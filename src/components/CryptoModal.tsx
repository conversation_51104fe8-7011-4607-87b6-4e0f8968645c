'use client';

import React from 'react';
import { X, TrendingUp, TrendingDown, ExternalLink } from 'lucide-react';
import { BubbleData } from '@/types/crypto';
import { useCoinDetails, useHistoricalData } from '@/hooks/useCrypto';
import { formatCurrency, formatPercentage } from '@/utils/dataTransform';
import LoadingSpinner from './LoadingSpinner';

interface CryptoModalProps {
  crypto: BubbleData | null;
  isOpen: boolean;
  onClose: () => void;
}

export default function CryptoModal({ crypto, isOpen, onClose }: CryptoModalProps) {
  const { coinDetails, isLoading: detailsLoading } = useCoinDetails(crypto?.id || null);
  const { historicalData, isLoading: historyLoading } = useHistoricalData(crypto?.id || null, 7);

  if (!isOpen || !crypto) return null;

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={handleBackdropClick}
    >
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <img 
              src={crypto.image} 
              alt={crypto.name}
              className="w-10 h-10 rounded-full"
            />
            <div>
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                {crypto.name}
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {crypto.symbol} • Rank #{crypto.rank}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Price Information */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <p className="text-sm text-gray-600 dark:text-gray-400">Current Price</p>
              <p className="text-lg font-bold text-gray-900 dark:text-white">
                {formatCurrency(crypto.price)}
              </p>
            </div>
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <p className="text-sm text-gray-600 dark:text-gray-400">24h Change</p>
              <div className="flex items-center space-x-1">
                {crypto.change >= 0 ? (
                  <TrendingUp className="w-4 h-4 text-green-500" />
                ) : (
                  <TrendingDown className="w-4 h-4 text-red-500" />
                )}
                <p className={`text-lg font-bold ${
                  crypto.change >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {formatPercentage(crypto.change)}
                </p>
              </div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <p className="text-sm text-gray-600 dark:text-gray-400">Market Cap</p>
              <p className="text-lg font-bold text-gray-900 dark:text-white">
                {formatCurrency(crypto.value)}
              </p>
            </div>
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <p className="text-sm text-gray-600 dark:text-gray-400">Volume (24h)</p>
              <p className="text-lg font-bold text-gray-900 dark:text-white">
                {formatCurrency(crypto.volume)}
              </p>
            </div>
          </div>

          {/* Additional Details */}
          {detailsLoading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner text="Loading details..." />
            </div>
          ) : coinDetails ? (
            <div className="space-y-4">
              {/* Description */}
              {coinDetails.description?.en && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    About {crypto.name}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
                    {coinDetails.description.en.split('.')[0]}.
                  </p>
                </div>
              )}

              {/* Market Data */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                  Market Data
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {coinDetails.market_data?.ath?.usd && (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">All-Time High:</span>
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {formatCurrency(coinDetails.market_data.ath.usd)}
                      </span>
                    </div>
                  )}
                  {coinDetails.market_data?.atl?.usd && (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">All-Time Low:</span>
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {formatCurrency(coinDetails.market_data.atl.usd)}
                      </span>
                    </div>
                  )}
                  {coinDetails.market_data?.circulating_supply && (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Circulating Supply:</span>
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {coinDetails.market_data.circulating_supply.toLocaleString()}
                      </span>
                    </div>
                  )}
                  {coinDetails.market_data?.max_supply && (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Max Supply:</span>
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {coinDetails.market_data.max_supply.toLocaleString()}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Links */}
              {coinDetails.links && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                    Links
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {coinDetails.links.homepage?.[0] && (
                      <a
                        href={coinDetails.links.homepage[0]}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center space-x-1 px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors"
                      >
                        <span>Website</span>
                        <ExternalLink className="w-3 h-3" />
                      </a>
                    )}
                    {coinDetails.links.twitter_screen_name && (
                      <a
                        href={`https://twitter.com/${coinDetails.links.twitter_screen_name}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center space-x-1 px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors"
                      >
                        <span>Twitter</span>
                        <ExternalLink className="w-3 h-3" />
                      </a>
                    )}
                    {coinDetails.links.repos_url?.github?.[0] && (
                      <a
                        href={coinDetails.links.repos_url.github[0]}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center space-x-1 px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-full text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                      >
                        <span>GitHub</span>
                        <ExternalLink className="w-3 h-3" />
                      </a>
                    )}
                  </div>
                </div>
              )}
            </div>
          ) : null}

          {/* Price History Chart Placeholder */}
          {historyLoading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner text="Loading price history..." />
            </div>
          ) : historicalData ? (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                7-Day Price History
              </h3>
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Price chart will be implemented in the next phase
                </p>
              </div>
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );
}
