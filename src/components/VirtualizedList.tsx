'use client';

import React, { useMemo } from 'react';
import { FixedSizeList as List } from 'react-window';
import { BubbleData } from '@/types/crypto';
import { formatCurrency, formatPercentage } from '@/utils/dataTransform';

interface VirtualizedListProps {
  data: BubbleData[];
  height: number;
  onItemClick?: (item: BubbleData) => void;
  className?: string;
}

interface ListItemProps {
  index: number;
  style: React.CSSProperties;
  data: {
    items: BubbleData[];
    onItemClick?: (item: BubbleData) => void;
  };
}

const ListItem: React.FC<ListItemProps> = ({ index, style, data }) => {
  const item = data.items[index];
  
  return (
    <div
      style={style}
      className="px-4 py-3 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors"
      onClick={() => data.onItemClick?.(item)}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3 flex-1 min-w-0">
          <div className="flex-shrink-0">
            <img
              src={item.image}
              alt={item.name}
              className="w-8 h-8 rounded-full"
            />
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-900 dark:text-white truncate">
                {item.name}
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400 uppercase">
                {item.symbol}
              </span>
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Rank #{item.rank}
            </div>
          </div>
        </div>
        
        <div className="flex items-center space-x-4 text-right">
          <div>
            <div className="text-sm font-medium text-gray-900 dark:text-white">
              {formatCurrency(item.price)}
            </div>
            <div className={`text-xs ${
              item.change >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {formatPercentage(item.change)}
            </div>
          </div>
          
          <div className="text-right">
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {formatCurrency(item.value)}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Market Cap
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default function VirtualizedList({
  data,
  height,
  onItemClick,
  className = '',
}: VirtualizedListProps) {
  const itemData = useMemo(() => ({
    items: data,
    onItemClick,
  }), [data, onItemClick]);

  if (data.length === 0) {
    return (
      <div 
        className={`flex items-center justify-center ${className}`}
        style={{ height }}
      >
        <div className="text-center">
          <div className="text-gray-500 dark:text-gray-400 mb-2">
            <svg className="w-12 h-12 mx-auto" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 001.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            No cryptocurrencies found
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden ${className}`}>
      <List
        height={height}
        itemCount={data.length}
        itemSize={80}
        itemData={itemData}
        className="bg-white dark:bg-gray-800"
      >
        {ListItem}
      </List>
    </div>
  );
}
