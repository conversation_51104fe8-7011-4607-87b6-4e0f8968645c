'use client';

import React, { useEffect, useRef, useState } from 'react';
import * as d3 from 'd3';
import { BubbleData } from '@/types/crypto';
import { getPriceChangeColor, formatCurrency, formatPercentage } from '@/utils/dataTransform';

interface TreemapChartProps {
  data: BubbleData[];
  width?: number;
  height?: number;
  onCellClick?: (data: BubbleData) => void;
  onCellHover?: (data: BubbleData | null) => void;
  className?: string;
}

interface TooltipData {
  data: BubbleData;
  x: number;
  y: number;
}

export default function TreemapChart({
  data,
  width = 800,
  height = 600,
  onCellClick,
  onCellHover,
  className = '',
}: TreemapChartProps) {
  const svgRef = useRef<SVGSVGElement>(null);
  const [tooltip, setTooltip] = useState<TooltipData | null>(null);

  useEffect(() => {
    if (!data.length || !svgRef.current) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    // Prepare data for treemap
    const root = d3.hierarchy({ children: data })
      .sum(d => d.value || 0)
      .sort((a, b) => (b.value || 0) - (a.value || 0));

    // Create treemap layout
    const treemap = d3.treemap()
      .size([width, height])
      .padding(2)
      .round(true);

    treemap(root);

    // Create cells
    const cells = svg
      .selectAll('g')
      .data(root.leaves())
      .enter()
      .append('g')
      .attr('transform', d => `translate(${d.x0},${d.y0})`);

    // Add rectangles
    cells
      .append('rect')
      .attr('width', d => d.x1 - d.x0)
      .attr('height', d => d.y1 - d.y0)
      .attr('fill', d => getPriceChangeColor(d.data.change))
      .attr('stroke', '#fff')
      .attr('stroke-width', 1)
      .style('cursor', 'pointer')
      .style('opacity', 0.8)
      .on('mouseover', function(event, d) {
        d3.select(this)
          .transition()
          .duration(200)
          .style('opacity', 1)
          .attr('stroke-width', 2);

        const [x, y] = d3.pointer(event, svgRef.current);
        setTooltip({ data: d.data, x, y });
        onCellHover?.(d.data);
      })
      .on('mouseout', function(event, d) {
        d3.select(this)
          .transition()
          .duration(200)
          .style('opacity', 0.8)
          .attr('stroke-width', 1);

        setTooltip(null);
        onCellHover?.(null);
      })
      .on('click', function(event, d) {
        onCellClick?.(d.data);
      });

    // Add text labels
    cells
      .append('text')
      .attr('x', 4)
      .attr('y', 16)
      .style('font-size', d => {
        const cellWidth = d.x1 - d.x0;
        const cellHeight = d.y1 - d.y0;
        const minDimension = Math.min(cellWidth, cellHeight);
        return `${Math.max(10, Math.min(14, minDimension / 8))}px`;
      })
      .style('font-weight', 'bold')
      .style('fill', '#fff')
      .style('pointer-events', 'none')
      .text(d => {
        const cellWidth = d.x1 - d.x0;
        return cellWidth > 60 ? d.data.symbol : '';
      });

    // Add price labels for larger cells
    cells
      .filter(d => (d.x1 - d.x0) > 80 && (d.y1 - d.y0) > 40)
      .append('text')
      .attr('x', 4)
      .attr('y', 32)
      .style('font-size', '11px')
      .style('fill', '#fff')
      .style('opacity', 0.9)
      .style('pointer-events', 'none')
      .text(d => formatCurrency(d.data.price));

    // Add change percentage for larger cells
    cells
      .filter(d => (d.x1 - d.x0) > 100 && (d.y1 - d.y0) > 60)
      .append('text')
      .attr('x', 4)
      .attr('y', 46)
      .style('font-size', '10px')
      .style('fill', '#fff')
      .style('opacity', 0.8)
      .style('pointer-events', 'none')
      .text(d => `${d.data.change >= 0 ? '+' : ''}${d.data.change.toFixed(2)}%`);

  }, [data, width, height, onCellClick, onCellHover]);

  return (
    <div className={`relative ${className}`}>
      <svg
        ref={svgRef}
        width={width}
        height={height}
        className="border border-gray-200 rounded-lg bg-white dark:bg-gray-900 dark:border-gray-700"
      />
      
      {tooltip && (
        <div
          className="absolute z-10 bg-black bg-opacity-90 text-white p-3 rounded-lg shadow-lg pointer-events-none"
          style={{
            left: tooltip.x + 10,
            top: tooltip.y - 10,
            transform: tooltip.x > width - 200 ? 'translateX(-100%)' : 'none',
          }}
        >
          <div className="font-bold text-lg">{tooltip.data.name}</div>
          <div className="text-sm text-gray-300">{tooltip.data.symbol}</div>
          <div className="mt-2 space-y-1">
            <div>Price: {formatCurrency(tooltip.data.price)}</div>
            <div>Market Cap: {formatCurrency(tooltip.data.value)}</div>
            <div>Volume: {formatCurrency(tooltip.data.volume)}</div>
            <div className={`${tooltip.data.change >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              24h Change: {formatPercentage(tooltip.data.change)}
            </div>
            <div className="text-xs text-gray-400">Rank #{tooltip.data.rank}</div>
          </div>
        </div>
      )}
    </div>
  );
}
