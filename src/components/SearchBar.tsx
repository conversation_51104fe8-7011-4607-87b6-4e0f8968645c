'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Search, X } from 'lucide-react';
import { useSearchCryptocurrencies } from '@/hooks/useCrypto';
import { debounce } from '@/utils/dataTransform';

interface SearchBarProps {
  onCryptoSelect?: (crypto: any) => void;
  placeholder?: string;
  className?: string;
}

export default function SearchBar({ 
  onCryptoSelect, 
  placeholder = "Search cryptocurrencies...",
  className = ""
}: SearchBarProps) {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [debouncedQuery, setDebouncedQuery] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const { searchResults, isLoading } = useSearchCryptocurrencies(debouncedQuery);

  // Debounce search query
  const debouncedSetQuery = debounce((value: string) => {
    setDebouncedQuery(value);
  }, 300);

  useEffect(() => {
    debouncedSetQuery(query);
  }, [query, debouncedSetQuery]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !inputRef.current?.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    setIsOpen(value.length > 0);
  };

  const handleCryptoSelect = (crypto: any) => {
    setQuery(crypto.name);
    setIsOpen(false);
    onCryptoSelect?.(crypto);
  };

  const clearSearch = () => {
    setQuery('');
    setDebouncedQuery('');
    setIsOpen(false);
    inputRef.current?.focus();
  };

  return (
    <div className={`relative ${className}`}>
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onFocus={() => query.length > 0 && setIsOpen(true)}
          placeholder={placeholder}
          className="block w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        {query && (
          <button
            onClick={clearSearch}
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
          >
            <X className="h-5 w-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" />
          </button>
        )}
      </div>

      {/* Search Results Dropdown */}
      {isOpen && (
        <div
          ref={dropdownRef}
          className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg max-h-60 overflow-y-auto"
        >
          {isLoading ? (
            <div className="px-4 py-3 text-sm text-gray-500 dark:text-gray-400">
              Searching...
            </div>
          ) : searchResults.length > 0 ? (
            <div className="py-1">
              {searchResults.slice(0, 10).map((crypto) => (
                <button
                  key={crypto.id}
                  onClick={() => handleCryptoSelect(crypto)}
                  className="w-full px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:bg-gray-50 dark:focus:bg-gray-600 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <img
                      src={crypto.large || crypto.thumb}
                      alt={crypto.name}
                      className="w-6 h-6 rounded-full"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {crypto.name}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400 uppercase">
                          {crypto.symbol}
                        </span>
                      </div>
                      {crypto.market_cap_rank && (
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          Rank #{crypto.market_cap_rank}
                        </div>
                      )}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          ) : debouncedQuery.length > 2 ? (
            <div className="px-4 py-3 text-sm text-gray-500 dark:text-gray-400">
              No cryptocurrencies found for "{debouncedQuery}"
            </div>
          ) : (
            <div className="px-4 py-3 text-sm text-gray-500 dark:text-gray-400">
              Type at least 3 characters to search
            </div>
          )}
        </div>
      )}
    </div>
  );
}
