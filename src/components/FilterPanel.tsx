'use client';

import React, { useState } from 'react';
import { Filter, ChevronDown, ChevronUp } from 'lucide-react';
import { FilterOptions } from '@/types/crypto';
import { MARKET_CAP_RANGES, VOLUME_RANGES, PRICE_CHANGE_RANGES } from '@/constants';

interface FilterPanelProps {
  filters: FilterOptions;
  onFiltersChange: (filters: FilterOptions) => void;
  className?: string;
}

export default function FilterPanel({ 
  filters, 
  onFiltersChange, 
  className = "" 
}: FilterPanelProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleMarketCapChange = (range: typeof MARKET_CAP_RANGES[0]) => {
    onFiltersChange({
      ...filters,
      minMarketCap: range.min === 0 ? undefined : range.min,
      maxMarketCap: range.max === Infinity ? undefined : range.max,
    });
  };

  const handleVolumeChange = (range: typeof VOLUME_RANGES[0]) => {
    onFiltersChange({
      ...filters,
      minVolume: range.min === 0 ? undefined : range.min,
      maxVolume: range.max === Infinity ? undefined : range.max,
    });
  };

  const handlePriceChangeChange = (range: typeof PRICE_CHANGE_RANGES[0]) => {
    onFiltersChange({
      ...filters,
      minPriceChange: range.min === -Infinity ? undefined : range.min,
      maxPriceChange: range.max === Infinity ? undefined : range.max,
    });
  };

  const clearFilters = () => {
    onFiltersChange({});
  };

  const hasActiveFilters = Object.keys(filters).some(key => 
    filters[key as keyof FilterOptions] !== undefined
  );

  return (
    <div className={`bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg ${className}`}>
      {/* Header */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full px-4 py-3 flex items-center justify-between text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
      >
        <div className="flex items-center space-x-2">
          <Filter className="w-5 h-5 text-gray-500" />
          <span className="font-medium text-gray-900 dark:text-white">
            Filters
          </span>
          {hasActiveFilters && (
            <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full">
              Active
            </span>
          )}
        </div>
        {isExpanded ? (
          <ChevronUp className="w-5 h-5 text-gray-500" />
        ) : (
          <ChevronDown className="w-5 h-5 text-gray-500" />
        )}
      </button>

      {/* Filter Content */}
      {isExpanded && (
        <div className="px-4 pb-4 space-y-4 border-t border-gray-200 dark:border-gray-700">
          {/* Market Cap Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Market Cap
            </label>
            <div className="space-y-1">
              {MARKET_CAP_RANGES.map((range) => (
                <label key={range.label} className="flex items-center">
                  <input
                    type="radio"
                    name="marketCap"
                    checked={
                      (filters.minMarketCap || 0) === range.min &&
                      (filters.maxMarketCap || Infinity) === range.max
                    }
                    onChange={() => handleMarketCapChange(range)}
                    className="mr-2 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {range.label}
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* Volume Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              24h Volume
            </label>
            <div className="space-y-1">
              {VOLUME_RANGES.map((range) => (
                <label key={range.label} className="flex items-center">
                  <input
                    type="radio"
                    name="volume"
                    checked={
                      (filters.minVolume || 0) === range.min &&
                      (filters.maxVolume || Infinity) === range.max
                    }
                    onChange={() => handleVolumeChange(range)}
                    className="mr-2 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {range.label}
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* Price Change Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              24h Price Change
            </label>
            <div className="space-y-1">
              {PRICE_CHANGE_RANGES.map((range) => (
                <label key={range.label} className="flex items-center">
                  <input
                    type="radio"
                    name="priceChange"
                    checked={
                      (filters.minPriceChange || -Infinity) === range.min &&
                      (filters.maxPriceChange || Infinity) === range.max
                    }
                    onChange={() => handlePriceChangeChange(range)}
                    className="mr-2 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {range.label}
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* Clear Filters */}
          {hasActiveFilters && (
            <button
              onClick={clearFilters}
              className="w-full px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              Clear All Filters
            </button>
          )}
        </div>
      )}
    </div>
  );
}
