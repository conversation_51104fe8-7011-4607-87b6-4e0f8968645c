import useSWR from 'swr';
import { CryptoCurrency, CoinDetails, HistoricalData, BubbleData } from '@/types/crypto';
import { CryptoAPI } from '@/lib/api';
import { transformToBubbleData } from '@/utils/dataTransform';

/**
 * Hook to fetch top cryptocurrencies
 */
export function useCryptocurrencies(
  limit: number = 100,
  page: number = 1,
  currency: string = 'usd'
) {
  const { data, error, isLoading, mutate } = useSWR(
    ['cryptocurrencies', limit, page, currency],
    () => CryptoAPI.getTopCryptocurrencies(limit, page, currency),
    {
      refreshInterval: 30000, // Refresh every 30 seconds
      revalidateOnFocus: true,
      dedupingInterval: 10000, // Dedupe requests within 10 seconds
    }
  );

  return {
    cryptocurrencies: data || [],
    isLoading,
    error,
    mutate,
  };
}

/**
 * Hook to fetch bubble chart data
 */
export function useBubbleData(
  limit: number = 100,
  page: number = 1,
  currency: string = 'usd'
) {
  const { cryptocurrencies, isLoading, error, mutate } = useCryptocurrencies(limit, page, currency);
  
  const bubbleData: BubbleData[] = cryptocurrencies ? transformToBubbleData(cryptocurrencies) : [];

  return {
    bubbleData,
    isLoading,
    error,
    mutate,
  };
}

/**
 * Hook to fetch coin details
 */
export function useCoinDetails(coinId: string | null) {
  const { data, error, isLoading } = useSWR(
    coinId ? ['coin-details', coinId] : null,
    () => coinId ? CryptoAPI.getCoinDetails(coinId) : null,
    {
      revalidateOnFocus: false,
      dedupingInterval: 60000, // Cache for 1 minute
    }
  );

  return {
    coinDetails: data,
    isLoading,
    error,
  };
}

/**
 * Hook to fetch historical data
 */
export function useHistoricalData(
  coinId: string | null,
  days: number = 30,
  currency: string = 'usd'
) {
  const { data, error, isLoading } = useSWR(
    coinId ? ['historical-data', coinId, days, currency] : null,
    () => coinId ? CryptoAPI.getHistoricalData(coinId, days, currency) : null,
    {
      revalidateOnFocus: false,
      dedupingInterval: 300000, // Cache for 5 minutes
    }
  );

  return {
    historicalData: data,
    isLoading,
    error,
  };
}

/**
 * Hook to search cryptocurrencies
 */
export function useSearchCryptocurrencies(query: string) {
  const { data, error, isLoading } = useSWR(
    query.length > 2 ? ['search', query] : null,
    () => query.length > 2 ? CryptoAPI.searchCryptocurrencies(query) : null,
    {
      revalidateOnFocus: false,
      dedupingInterval: 30000,
    }
  );

  return {
    searchResults: data || [],
    isLoading,
    error,
  };
}

/**
 * Hook to fetch global market data
 */
export function useGlobalData() {
  const { data, error, isLoading } = useSWR(
    'global-data',
    CryptoAPI.getGlobalData,
    {
      refreshInterval: 60000, // Refresh every minute
      revalidateOnFocus: true,
    }
  );

  return {
    globalData: data,
    isLoading,
    error,
  };
}

/**
 * Hook to fetch trending cryptocurrencies
 */
export function useTrendingCryptocurrencies() {
  const { data, error, isLoading } = useSWR(
    'trending',
    CryptoAPI.getTrendingCryptocurrencies,
    {
      refreshInterval: 300000, // Refresh every 5 minutes
      revalidateOnFocus: true,
    }
  );

  return {
    trending: data || [],
    isLoading,
    error,
  };
}
