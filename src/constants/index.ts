import { VisualizationMode } from '@/types/crypto';

export const VISUALIZATION_MODES: VisualizationMode[] = [
  {
    id: 'bubble',
    name: 'Bubble Chart',
    description: 'Interactive bubble chart where size represents market cap and color represents price change',
  },
  {
    id: 'treemap',
    name: 'Treemap',
    description: 'Hierarchical visualization showing market cap proportions',
  },
  {
    id: 'scatter',
    name: 'Scatter Plot',
    description: 'Scatter plot showing price vs volume relationships',
  },
  {
    id: 'heatmap',
    name: 'Heatmap',
    description: 'Grid-based heatmap showing price changes across different time periods',
  },
];

export const SUPPORTED_CURRENCIES = [
  { code: 'usd', name: 'US Dollar', symbol: '$' },
  { code: 'eur', name: 'Euro', symbol: '€' },
  { code: 'gbp', name: 'British Pound', symbol: '£' },
  { code: 'jpy', name: 'Japanese Yen', symbol: '¥' },
  { code: 'btc', name: 'Bitcoin', symbol: '₿' },
  { code: 'eth', name: 'Ethereum', symbol: 'Ξ' },
];

export const TIME_PERIODS = [
  { value: 1, label: '24H', days: 1 },
  { value: 7, label: '7D', days: 7 },
  { value: 30, label: '30D', days: 30 },
  { value: 90, label: '90D', days: 90 },
  { value: 365, label: '1Y', days: 365 },
];

export const MARKET_CAP_RANGES = [
  { label: 'All', min: 0, max: Infinity },
  { label: 'Large Cap (>$10B)', min: 10e9, max: Infinity },
  { label: 'Mid Cap ($1B-$10B)', min: 1e9, max: 10e9 },
  { label: 'Small Cap ($100M-$1B)', min: 100e6, max: 1e9 },
  { label: 'Micro Cap (<$100M)', min: 0, max: 100e6 },
];

export const VOLUME_RANGES = [
  { label: 'All', min: 0, max: Infinity },
  { label: 'High Volume (>$1B)', min: 1e9, max: Infinity },
  { label: 'Medium Volume ($100M-$1B)', min: 100e6, max: 1e9 },
  { label: 'Low Volume (<$100M)', min: 0, max: 100e6 },
];

export const PRICE_CHANGE_RANGES = [
  { label: 'All', min: -Infinity, max: Infinity },
  { label: 'Strong Gainers (>+10%)', min: 10, max: Infinity },
  { label: 'Gainers (+5% to +10%)', min: 5, max: 10 },
  { label: 'Moderate (+0% to +5%)', min: 0, max: 5 },
  { label: 'Moderate (-5% to 0%)', min: -5, max: 0 },
  { label: 'Losers (-10% to -5%)', min: -10, max: -5 },
  { label: 'Strong Losers (<-10%)', min: -Infinity, max: -10 },
];

export const BUBBLE_CONFIG = {
  MIN_RADIUS: 8,
  MAX_RADIUS: 80,
  PADDING: 2,
  ANIMATION_DURATION: 750,
  COLLISION_STRENGTH: 0.7,
  CHARGE_STRENGTH: -30,
};

export const COLORS = {
  POSITIVE: {
    STRONG: '#00C851',
    MEDIUM: '#00FF41',
    LIGHT: '#4CAF50',
  },
  NEGATIVE: {
    STRONG: '#FF1744',
    MEDIUM: '#FF5722',
    LIGHT: '#F44336',
  },
  NEUTRAL: '#9E9E9E',
  BACKGROUND: {
    LIGHT: '#FFFFFF',
    DARK: '#1A1A1A',
  },
  TEXT: {
    PRIMARY_LIGHT: '#000000',
    PRIMARY_DARK: '#FFFFFF',
    SECONDARY_LIGHT: '#666666',
    SECONDARY_DARK: '#CCCCCC',
  },
};

export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
};

export const DEFAULT_SETTINGS = {
  currency: 'usd',
  theme: THEMES.LIGHT,
  refreshInterval: 30000,
  itemsPerPage: 100,
  visualizationMode: 'bubble' as const,
  showLabels: true,
  showTooltips: true,
  enableAnimations: true,
};

export const API_ENDPOINTS = {
  COINGECKO_BASE: 'https://api.coingecko.com/api/v3',
  RATE_LIMIT_DELAY: 100, // milliseconds
  TIMEOUT: 10000, // milliseconds
};

export const LOCAL_STORAGE_KEYS = {
  THEME: 'crypto-bubble-theme',
  CURRENCY: 'crypto-bubble-currency',
  SETTINGS: 'crypto-bubble-settings',
  PORTFOLIO: 'crypto-bubble-portfolio',
  FAVORITES: 'crypto-bubble-favorites',
};

export const SOCIAL_LINKS = {
  TWITTER: 'https://twitter.com',
  TELEGRAM: 'https://t.me',
  REDDIT: 'https://reddit.com',
  GITHUB: 'https://github.com',
};

export const EXPORT_FORMATS = [
  { value: 'png', label: 'PNG Image' },
  { value: 'svg', label: 'SVG Vector' },
  { value: 'pdf', label: 'PDF Document' },
  { value: 'csv', label: 'CSV Data' },
  { value: 'json', label: 'JSON Data' },
];

export const CHART_DIMENSIONS = {
  DEFAULT_WIDTH: 800,
  DEFAULT_HEIGHT: 600,
  MIN_WIDTH: 400,
  MIN_HEIGHT: 300,
  ASPECT_RATIO: 4 / 3,
};

export const ANIMATION_PRESETS = {
  FAST: { duration: 300, easing: 'ease-out' },
  NORMAL: { duration: 500, easing: 'ease-in-out' },
  SLOW: { duration: 750, easing: 'ease-in-out' },
  SPRING: { type: 'spring', stiffness: 300, damping: 30 },
};
