'use client';

import React, { useState, useMemo } from 'react';
import B<PERSON>ble<PERSON>hart from '@/components/BubbleChart';
import LoadingSpinner, { BubbleChartSkeleton } from '@/components/LoadingSpinner';
import ErrorDisplay from '@/components/ErrorBoundary';
import Header from '@/components/Header';
import CryptoModal from '@/components/CryptoModal';
import FilterPanel from '@/components/FilterPanel';
import VisualizationModeSelector from '@/components/VisualizationModeSelector';
import ChartContainer from '@/components/ChartContainer';
import { useCryptocurrencies } from '@/hooks/useCrypto';
import { BubbleData, FilterOptions, VisualizationMode } from '@/types/crypto';
import { filterCryptocurrencies, transformToBubbleData } from '@/utils/dataTransform';

export default function Home() {
  const [selectedCrypto, setSelectedCrypto] = useState<BubbleData | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [filters, setFilters] = useState<FilterOptions>({});
  const [visualizationMode, setVisualizationMode] = useState<VisualizationMode['id']>('bubble');
  const { cryptocurrencies, isLoading, error, mutate } = useCryptocurrencies(100);

  // Apply filters to cryptocurrency data
  const filteredCryptocurrencies = useMemo(() => {
    if (!cryptocurrencies) return [];
    return filterCryptocurrencies(cryptocurrencies, filters);
  }, [cryptocurrencies, filters]);

  // Transform to bubble data
  const bubbleData = useMemo(() => {
    return transformToBubbleData(filteredCryptocurrencies);
  }, [filteredCryptocurrencies]);

  const handleBubbleClick = (data: BubbleData) => {
    setSelectedCrypto(data);
    setIsModalOpen(true);
  };

  const handleBubbleHover = (data: BubbleData | null) => {
    // Handle hover events if needed
  };

  const handleCryptoSearch = (crypto: any) => {
    // Find the crypto in our data and select it
    const foundCrypto = bubbleData.find(c => c.id === crypto.id);
    if (foundCrypto) {
      setSelectedCrypto(foundCrypto);
      setIsModalOpen(true);
    }
  };



  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <ErrorDisplay
          error={error}
          onRetry={() => mutate()}
          className="max-w-md"
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <Header
        onRefresh={() => mutate()}
        isRefreshing={isLoading}
        onCryptoSearch={handleCryptoSearch}
      />

        {/* Main Content */}
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Chart Area */}
            <div className="lg:col-span-3 space-y-6">
              {/* Controls Row */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {/* Filter Panel */}
                <FilterPanel
                  filters={filters}
                  onFiltersChange={setFilters}
                />

                {/* Visualization Mode Selector */}
                <VisualizationModeSelector
                  currentMode={visualizationMode}
                  onModeChange={setVisualizationMode}
                />
              </div>

              {/* Chart Container */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <div className="mb-4 flex items-center justify-between">
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                      Market Overview
                    </h2>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {visualizationMode === 'bubble' && 'Bubble size represents market cap, color represents 24h price change'}
                      {visualizationMode === 'treemap' && 'Rectangle size represents market cap, color represents 24h price change'}
                      {visualizationMode === 'scatter' && 'X-axis: Volume, Y-axis: Price, color represents 24h price change'}
                      {visualizationMode === 'heatmap' && 'Grid showing price changes across different time periods'}
                    </p>
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Showing {bubbleData.length} cryptocurrencies
                  </div>
                </div>

                <ChartContainer
                  data={bubbleData}
                  mode={visualizationMode}
                  width={800}
                  height={600}
                  isLoading={isLoading}
                  onItemClick={handleBubbleClick}
                  onItemHover={handleBubbleHover}
                  className="w-full"
                />
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Market Stats
                </h3>

                <div className="space-y-4">
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Click on a bubble to view detailed information about a cryptocurrency.
                  </p>

                  {!isLoading && bubbleData.length > 0 && (
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Total Coins:</span>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {bubbleData.length}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Gainers:</span>
                        <span className="text-sm font-medium text-green-600">
                          {bubbleData.filter(d => d.change > 0).length}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Losers:</span>
                        <span className="text-sm font-medium text-red-600">
                          {bubbleData.filter(d => d.change < 0).length}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Neutral:</span>
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          {bubbleData.filter(d => d.change === 0).length}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </main>

        {/* Crypto Details Modal */}
        <CryptoModal
          crypto={selectedCrypto}
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setSelectedCrypto(null);
          }}
        />
    </div>
  );
}
