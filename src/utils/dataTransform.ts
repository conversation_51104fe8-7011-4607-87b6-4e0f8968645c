import { CryptoCurrency, BubbleData, FilterOptions } from '@/types/crypto';

/**
 * Transform cryptocurrency data into bubble chart format
 */
export function transformToBubbleData(cryptos: CryptoCurrency[]): BubbleData[] {
  return cryptos.map((crypto) => ({
    id: crypto.id,
    symbol: crypto.symbol.toUpperCase(),
    name: crypto.name,
    value: crypto.market_cap || 0,
    change: crypto.price_change_percentage_24h || 0,
    price: crypto.current_price || 0,
    volume: crypto.total_volume || 0,
    rank: crypto.market_cap_rank || 0,
    image: crypto.image,
  }));
}

/**
 * Calculate bubble radius based on market cap
 */
export function calculateBubbleRadius(
  marketCap: number,
  minRadius: number = 10,
  maxRadius: number = 100,
  allMarketCaps: number[] = []
): number {
  if (allMarketCaps.length === 0) {
    return minRadius;
  }

  const minMarketCap = Math.min(...allMarketCaps);
  const maxMarketCap = Math.max(...allMarketCaps);

  if (maxMarketCap === minMarketCap) {
    return minRadius;
  }

  // Use square root scale for better visual distribution
  const normalizedValue = Math.sqrt(marketCap - minMarketCap) / Math.sqrt(maxMarketCap - minMarketCap);
  return minRadius + (maxRadius - minRadius) * normalizedValue;
}

/**
 * Get color based on price change percentage
 */
export function getPriceChangeColor(change: number): string {
  if (change > 0) {
    // Green shades for positive changes
    if (change > 10) return '#00C851'; // Strong green
    if (change > 5) return '#00FF41'; // Medium green
    return '#4CAF50'; // Light green
  } else if (change < 0) {
    // Red shades for negative changes
    if (change < -10) return '#FF1744'; // Strong red
    if (change < -5) return '#FF5722'; // Medium red
    return '#F44336'; // Light red
  }
  return '#9E9E9E'; // Gray for no change
}

/**
 * Filter cryptocurrencies based on criteria
 */
export function filterCryptocurrencies(
  cryptos: CryptoCurrency[],
  filters: FilterOptions
): CryptoCurrency[] {
  return cryptos.filter((crypto) => {
    // Market cap filter
    if (filters.minMarketCap && crypto.market_cap < filters.minMarketCap) {
      return false;
    }
    if (filters.maxMarketCap && crypto.market_cap > filters.maxMarketCap) {
      return false;
    }

    // Volume filter
    if (filters.minVolume && crypto.total_volume < filters.minVolume) {
      return false;
    }
    if (filters.maxVolume && crypto.total_volume > filters.maxVolume) {
      return false;
    }

    // Price change filter
    if (filters.minPriceChange && crypto.price_change_percentage_24h < filters.minPriceChange) {
      return false;
    }
    if (filters.maxPriceChange && crypto.price_change_percentage_24h > filters.maxPriceChange) {
      return false;
    }

    // Search query filter
    if (filters.searchQuery) {
      const query = filters.searchQuery.toLowerCase();
      const nameMatch = crypto.name.toLowerCase().includes(query);
      const symbolMatch = crypto.symbol.toLowerCase().includes(query);
      if (!nameMatch && !symbolMatch) {
        return false;
      }
    }

    return true;
  });
}

/**
 * Sort cryptocurrencies by various criteria
 */
export function sortCryptocurrencies(
  cryptos: CryptoCurrency[],
  sortBy: 'market_cap' | 'price' | 'volume' | 'change_24h' | 'name',
  order: 'asc' | 'desc' = 'desc'
): CryptoCurrency[] {
  return [...cryptos].sort((a, b) => {
    let aValue: number | string;
    let bValue: number | string;

    switch (sortBy) {
      case 'market_cap':
        aValue = a.market_cap || 0;
        bValue = b.market_cap || 0;
        break;
      case 'price':
        aValue = a.current_price || 0;
        bValue = b.current_price || 0;
        break;
      case 'volume':
        aValue = a.total_volume || 0;
        bValue = b.total_volume || 0;
        break;
      case 'change_24h':
        aValue = a.price_change_percentage_24h || 0;
        bValue = b.price_change_percentage_24h || 0;
        break;
      case 'name':
        aValue = a.name.toLowerCase();
        bValue = b.name.toLowerCase();
        break;
      default:
        aValue = a.market_cap || 0;
        bValue = b.market_cap || 0;
    }

    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return order === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
    }

    const numA = aValue as number;
    const numB = bValue as number;
    return order === 'asc' ? numA - numB : numB - numA;
  });
}

/**
 * Format currency values
 */
export function formatCurrency(value: number, currency: string = 'USD'): string {
  if (value === 0) return '$0';
  
  if (value >= 1e12) {
    return `$${(value / 1e12).toFixed(2)}T`;
  }
  if (value >= 1e9) {
    return `$${(value / 1e9).toFixed(2)}B`;
  }
  if (value >= 1e6) {
    return `$${(value / 1e6).toFixed(2)}M`;
  }
  if (value >= 1e3) {
    return `$${(value / 1e3).toFixed(2)}K`;
  }
  
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: value < 1 ? 4 : 2,
    maximumFractionDigits: value < 1 ? 4 : 2,
  }).format(value);
}

/**
 * Format percentage values
 */
export function formatPercentage(value: number): string {
  if (value === 0) return '0.00%';
  
  return new Intl.NumberFormat('en-US', {
    style: 'percent',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value / 100);
}

/**
 * Calculate portfolio statistics
 */
export function calculatePortfolioStats(holdings: any[]): {
  totalValue: number;
  totalChange24h: number;
  totalChangePercentage24h: number;
  bestPerformer: any | null;
  worstPerformer: any | null;
} {
  if (holdings.length === 0) {
    return {
      totalValue: 0,
      totalChange24h: 0,
      totalChangePercentage24h: 0,
      bestPerformer: null,
      worstPerformer: null,
    };
  }

  const totalValue = holdings.reduce((sum, holding) => sum + holding.value, 0);
  const totalChange24h = holdings.reduce((sum, holding) => sum + holding.change24h, 0);
  const totalChangePercentage24h = totalValue > 0 ? (totalChange24h / (totalValue - totalChange24h)) * 100 : 0;

  const sortedByPerformance = [...holdings].sort((a, b) => b.changePercentage24h - a.changePercentage24h);
  const bestPerformer = sortedByPerformance[0];
  const worstPerformer = sortedByPerformance[sortedByPerformance.length - 1];

  return {
    totalValue,
    totalChange24h,
    totalChangePercentage24h,
    bestPerformer,
    worstPerformer,
  };
}

/**
 * Debounce function for search inputs
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}
